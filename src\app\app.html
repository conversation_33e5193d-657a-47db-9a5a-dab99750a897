<!-- Navigation -->
<nav style="padding: 20px; background-color: #f5f5f5; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
  <div style="max-width: 1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between;">
    <h1 style="margin: 0; color: #333; font-size: 1.5rem;">Angular Material Demo App</h1>
    <div>
      <a routerLink="/demo" style="margin-right: 20px; text-decoration: none; color: #1976d2; font-weight: bold; padding: 8px 16px; border-radius: 4px; transition: background-color 0.3s;">Demo Component</a>
    </div>
  </div>
</nav>


<!-- Router Outlet - Nơi component sẽ được hiển thị -->
<div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
  <router-outlet />
</div>

